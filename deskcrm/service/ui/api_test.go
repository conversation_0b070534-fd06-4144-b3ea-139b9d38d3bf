package ui

import (
	"deskcrm/api/assistantdesk"
	"deskcrm/api/dal"
	"deskcrm/components"
	"deskcrm/consts"
	"deskcrm/controllers/http/ui/input/inputKeepDetail"
	"deskcrm/controllers/http/ui/input/inputStudent"
	"deskcrm/controllers/http/ui/output/outputStudent"
	"deskcrm/helpers"
	"deskcrm/libs/utils"
	courseRecord2 "deskcrm/service/innerapi/courseRecord"
	"deskcrm/service/ui/apiTest"
	"deskcrm/stru/keepDetail"
	"deskcrm/util"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"path"
	"reflect"
	"runtime"
	"testing"
	"time"

	"strings"

	"bou.ke/monkey"
	commonArkGo "git.zuoyebang.cc/fwyybase/fwyylibs/api/arkgo"
	baseTower "git.zuoyebang.cc/fwyybase/fwyylibs/api/tower"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/stretchr/testify/assert"
)

func getSourcePath() string {
	_, filename, _, _ := runtime.Caller(1)
	return path.Dir(filename)
}

func init() {
	dir := getSourcePath()
	env.SetAppName("testing")
	env.SetRootPath(dir + "/../..")

	helpers.PreInit()
	helpers.InitMysql()
	helpers.InitValidator()
	helpers.InitApiClient()
	helpers.InitRedis()
}

func createCtx() *gin.Context {
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	req, _ := http.NewRequest("GET", "/", nil)
	req.AddCookie(&http.Cookie{
		Name:  "ZYBIPSCAS",
		Value: "IPS_29d88adfe87f4d56346cd7244b0154a01754966600",
	})

	ctx.Request = req
	ctx.Set("isDebug", true)
	return ctx
}

func TestStudentService_StudentDetailV1(t *testing.T) {
	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx := createCtx()

	// 设置输入参数
	param := &inputStudent.StudentDetailV1Param{
		StudentUid:   4522614519,
		CourseId:     3036978,
		LeadsId:      1588837335,
		AssistantUid: 4201074003,
		PersonUid:    3000147384,
		Tags:         "tagUserType,tagGuardianWechatLight,tagIsTransferStudent,tagIsTransferCourse,tagIsLevelTwo,tagIsBound,tagIsTransferStudentDelayed",
	}

	if param.Tags != "" {
		param.TagArr = strings.Split(param.Tags, ",")
	}

	// 先不测走方舟规则的这种
	var mockRules commonArkGo.GetFieldRuleByKeysResp = getDefineRuleMap(ctx)

	patch1 := monkey.Patch(commonArkGo.GetFieldRuleByKeys,
		func(ctx *gin.Context, param commonArkGo.GetFieldRuleByKeysParam) (*commonArkGo.GetFieldRuleByKeysResp, error) {
			return &mockRules, nil
		})
	defer patch1.Unpatch()

	service := studentService{}
	resp, err := service.StudentDetailV1(ctx, param)

	components.DebugfWithJSON(ctx, "StudentDetailV1 resp: %s", resp)

	assert.Nil(t, err)
	assert.NotEmpty(t, resp)
}

func TestStudentService_CourseRecordV2(t *testing.T) {
	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx := createCtx()

	// 设置输入参数
	p := &inputStudent.CourseRecordV2Param{
		StudentUid:        2285235952,
		CourseId:          3067958,
		LeadsId:           1561215123,
		Category:          3,
		OnlyCurrentCourse: 1,
		AssistantUid:      4312624260,
		PersonUid:         2136917703,
	}

	if p.Year == 0 {
		p.Year = consts.YearAll
	}

	if p.Season == 0 {
		p.Season = consts.SeasonAll
	}

	if p.BuyType == 0 {
		p.BuyType = consts.BuyAll
	}

	if len(p.NewCourseTypes) > 0 {
		err := jsoniter.UnmarshalFromString(p.NewCourseTypes, &p.NewCourseTypesArr)
		if err != nil {
			zlog.Error(ctx, "UnmarshalFromString err: %v", err)
			return
		}
	}

	if p.Year == consts.YearAll {
		now := time.Now().Unix()
		p.StartTime = now - 365*86400
		p.EndTime = now
	} else {
		timeStr := fmt.Sprintf("%d-01-01", p.Year)
		yearStart, _ := time.Parse("2006-01-02", timeStr)
		p.StartTime = yearStart.Unix() - 180*86400
		p.EndTime = yearStart.Unix() + 365*86400
	}

	// 先不测走方舟规则的这种
	var mockRules commonArkGo.GetFieldRuleByAppResp = getDefineRuleMap(ctx)

	patch1 := monkey.Patch(commonArkGo.GetFieldRuleByApp,
		func(ctx *gin.Context, param commonArkGo.GetFieldRuleByAppParam) (*commonArkGo.GetFieldRuleByAppResp, error) {
			return &mockRules, nil
		})
	defer patch1.Unpatch()

	mockService := &courseRecord2.ConfigCourseRecordService{}
	patch2 := monkey.Patch(courseRecord2.NewConfigServiceCourseRecord,
		func() *courseRecord2.ConfigCourseRecordService {
			return mockService
		})

	patch3 := monkey.PatchInstanceMethod(reflect.TypeOf(mockService), "GetShouldersAndTagsAndTabsByCourseIds",
		func(cs *courseRecord2.ConfigCourseRecordService, ctx *gin.Context, courseList []dal.CourseInfo, towerCourseMap map[int64]baseTower.GetCourseInfoRsp) (keepDetail.CourseSchemaResponse, error) {
			mockResponse := keepDetail.CourseSchemaResponse{
				3067958: keepDetail.CourseSchema{
					Shoulders: []string{
						"stageTest",
						"stageReport",
						"stageReportLpc",
						"studyReport",
						"checkTest",
						"totalScore",
						"bookSend",
						"examErrorButton",
						"errorTaskButton",
					},
					ShoulderTags: []string{
						"isL2rSpaceSeason",
						"hasLateEnrollee",
						"tagRefund",
					},
					TabData: map[string]keepDetail.TabHeaders{
						"coreData": {
							Headers: []keepDetail.Header{
								{
									Value:      "lessonName",
									Label:      "章节名称",
									Width:      "280",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "startTime",
									Label:      "上课时间",
									Width:      "140",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "attend",
									Label:      "到课",
									Width:      "80",
									Hover:      "学员若请假且未到课，则展现学员请假。若学员到课，则展示学员直播课环节中累计到课时长。",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "exercise",
									Label:      "直播互动题",
									Width:      "90",
									Hover:      "展现学员直播过程中参与的互动题作答正确数、作答数、试卷题目总数信息。",
									Remark:     "通用",
									CustomName: "",
								},
								{
									Value:      "exerciseAll",
									Label:      "观看互动题",
									Width:      "90",
									Hover:      " 展现学员直播+回放过程中参与的互动题作答正确数、作答数、试卷题目总数信息。",
									Remark:     "通用",
									CustomName: "",
								},
								{
									Value:      "playbackv1",
									Label:      "回放观看时长(新)",
									Width:      "80",
									Hover:      "展示的是回放或LBP的累计观看时长。前60天实时更新，60天之后的数据每日更新一次",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "homework",
									Label:      "巩固练习",
									Width:      "80",
									Hover:      "普通课程巩固练习作答评级，分为S/A/B三类。ilab课程展现学员巩固练习成绩，分为优秀良好一般三类",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "preview",
									Label:      "预习",
									Width:      "80",
									Hover:      "普通课程展现作答正确数、作答数、试卷题目总数。ilab课程展现优秀、良好、一般三类评级。",
									Remark:     "原辅导",
									CustomName: "",
								},
							},
						},
						"ktbx": {
							Headers: []keepDetail.Header{
								{
									Value:      "lessonName",
									Label:      "章节名称",
									Width:      "300",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "startTime",
									Label:      "上课时间",
									Width:      "200",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "talk",
									Label:      "课中聊天",
									Width:      "100",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "monthlyExamReport",
									Label:      "考试报告",
									Width:      "100",
									Hover:      "需在学员提交堂堂测之后才会生成考试报告哦。",
									Remark:     "原辅导",
									CustomName: "",
								},
							},
						},
						"mryl": {
							Headers: []keepDetail.Header{},
						},
					},
					SchemaID: 43,
				},
			}
			return mockResponse, nil
		})
	defer patch2.Unpatch()
	defer patch3.Unpatch()

	// 执行被测试的方法
	service := studentService{}
	resp, err := service.CourseRecordV2(ctx, p)

	// 输出调试信息
	components.DebugfWithJSON(ctx, "CourseRecordV2 resp: %s", resp)

	// 断言测试结果
	assert.Nil(t, err)
	assert.NotNil(t, resp)
}

func TestStudentService_StudentDelaminationDifferenceListV1(t *testing.T) {
	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx := createCtx()

	// 设置输入参数
	param := inputStudent.StudentDelaminationDifferenceListV1Param{
		StudentUid:   2701721965,
		CourseId:     3259316,
		AssistantUid: 4314768242,
		PersonUid:    3000160829,
		PageSize:     5,
		Page:         1,
		Keys:         "finshLessonRateCommon,assistantCourseAttendFinishRateCommon,assistantCourseAttendRateCommon,attendLongLessonNumTimelessRateCommon,playback_participate_rate_new_common,playback_right_rate_new_common,consolidationExamRightRateCommon,consolidationExamFinishRateCommon,consolidationExamSRateCommon,homeworkTidFirstRightCntRateCommon,sent_total_num_common,group_user_message_sent_total_num_common,user_sent_total_num_common,total_voice_chat_duration_common,validSessionTotalNumCommon,groupUserMessageSentTotalNumCommon,totalVoiceChatNumCommon,wechatReplyRateCommon",
	}

	if param.PageSize == 0 {
		param.PageSize = 1
	}

	if param.PageSize > 5 {
		param.PageSize = 5
	}

	if param.Page == 0 {
		param.Page = 1
	}

	keysMap := make(map[string]struct{})
	param.KeysArray = strings.Split(param.Keys, ",")

	for _, key := range param.KeysArray {
		keysMap[key] = struct{}{}
	}
	param.KeysMap = keysMap

	// 先不测走方舟规则的这种
	var mockRules commonArkGo.GetFieldRuleByAppResp = getDefineRuleMap(ctx)

	patch1 := monkey.Patch(commonArkGo.GetFieldRuleByApp,
		func(ctx *gin.Context, param commonArkGo.GetFieldRuleByAppParam) (*commonArkGo.GetFieldRuleByAppResp, error) {
			return &mockRules, nil
		})
	defer patch1.Unpatch()

	service := studentService{}
	resp, err := service.StudentDelaminationDifferenceListV1(ctx, param)

	components.DebugfWithJSON(ctx, "StudentDelaminationDifferenceListV1 resp: %s", resp)

	assert.Nil(t, err)
	assert.NotEmpty(t, resp)
}

func getDefineRuleMap(ctx *gin.Context) map[string]*commonArkGo.RuleFieldStruct {
	res := make(map[string]*apiTest.RuleFieldStruct)
	err := json.Unmarshal([]byte(apiTest.RuleStr), &res)
	if err != nil {
		return nil
	}
	data := make(map[string]*commonArkGo.RuleFieldStruct)
	for key, val := range res {

		s, _ := apiTest.DecodeObjectOrArray(ctx, val.ServiceConfig)

		data[key] = &commonArkGo.RuleFieldStruct{
			ID:            val.ID,
			ModuleID:      val.ModuleID,
			RuleName:      val.RuleName,
			ServiceConfig: s,
			Key:           val.Key,
			OriName:       val.OriName,
			CustomName:    val.CustomName,
			ArkType:       val.ArkType,
			Info:          val.Info,
			Function:      val.Function,
		}
	}
	return data
}

func TestStudentService_GetStudentCallInfo(t *testing.T) {
	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx := createCtx()

	// 设置输入参数
	param := inputKeepDetail.StudentCallInfoParams{
		AssistantUid: 4314768242,
		PersonUid:    3000160829,
		PageType:     1,
		PageTab:      1, //0.1.2
		CourseID:     3259316,
		StudentUID:   2701721965,
		Page:         1,
		PageSize:     10,
	}

	param.CallType = consts.CALL_TYPE
	if newType, ok := consts.CallTypeMap[param.CallType]; ok {
		param.CallType = newType
	}

	service := keepDetailService{}
	resp, err := service.GetStudentCallInfo(ctx, param)

	components.DebugfWithJSON(ctx, "GetStudentCallInfo resp: %s", resp)

	assert.Nil(t, err)
	assert.NotEmpty(t, resp)
}

func TestStudentService_GetStudentBind(t *testing.T) {
	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx := createCtx()

	service := keepDetailService{}
	resp, err := service.GetStudentBind(ctx, 3036978, 4522614519, 4201074003)

	components.DebugfWithJSON(ctx, "GetStudentBind resp: %s", resp)

	assert.Nil(t, err)
	assert.NotEmpty(t, resp)
}

func TestStudentService_GetActiveWithBindData(t *testing.T) {
	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx := createCtx()

	service := keepDetailService{}
	resp, err := service.GetActiveWithBindData(ctx, 3036978, 4522614519, 4201074003, 1)

	components.DebugfWithJSON(ctx, "GetActiveWithBindData resp: %s", resp)

	assert.Nil(t, err)
	assert.NotEmpty(t, resp)
}

func TestStudentService_GetCallTypeList(t *testing.T) {
	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx := createCtx()

	service := keepDetailService{}
	resp, err := service.GetCallTypeList(ctx)

	components.DebugfWithJSON(ctx, "GetCallTypeList resp: %s", resp)

	assert.Nil(t, err)
	assert.NotEmpty(t, resp)
}

func TestStudentService_AllowAutoCallAndMessage(t *testing.T) {
	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx := createCtx()

	service := courseService{}
	resp, err := service.AllowAutoCallAndMessage(ctx, 3036978)

	components.DebugfWithJSON(ctx, "AllowAutoCallAndMessage resp: %s", resp)

	assert.Nil(t, err)
	assert.NotEmpty(t, resp)
}

func TestStudentService_GetSipInfo(t *testing.T) {
	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx := createCtx()

	service := courseService{}
	resp, err := service.GetSipInfo(ctx, 3036978, "", 4201074003, 3000147384)

	components.DebugfWithJSON(ctx, "GetSipInfo resp: %s", resp)

	assert.Nil(t, err)
	assert.NotEmpty(t, resp)
}

func TestStudentService_GetSchemaByCourseId(t *testing.T) {
	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx := createCtx()

	service := keepDetailService{}
	resp, err := service.GetSchemaByCourseId(ctx, 3036978)

	components.DebugfWithJSON(ctx, "GetSchemaByCourseId resp: %s", resp)

	assert.Nil(t, err)
	assert.NotEmpty(t, resp)
}

func TestPerformanceService_GetPerformanceV1(t *testing.T) {
	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx := createCtx()

	// 设置输入参数
	p := &inputStudent.PerformanceV1Param{
		StudentUid:   4452570490,
		CourseId:     3740621,
		LeadsId:      0,
		Tab:          consts.TAB_CORE_DATA,
		AssistantUid: 4312624260,
		PersonUid:    2136917703,
		IsExport:     0,
	}

	// Mock方舟规则
	mockRules := &commonArkGo.GetFieldRuleByAppResp{
		"tag1": {},
		"tag2": {},
	}

	patch1 := monkey.Patch(commonArkGo.GetFieldRuleByApp,
		func(ctx *gin.Context, param commonArkGo.GetFieldRuleByAppParam) (*commonArkGo.GetFieldRuleByAppResp, error) {
			return mockRules, nil
		})
	defer patch1.Unpatch()

	// Mock课程记录配置服务
	mockService := &courseRecord2.ConfigCourseRecordService{}
	patch2 := monkey.Patch(courseRecord2.NewConfigServiceCourseRecord,
		func() *courseRecord2.ConfigCourseRecordService {
			return mockService
		})

	// Mock GetShouldersAndTagsAndTabsByCourseId方法
	patch3 := monkey.PatchInstanceMethod(reflect.TypeOf(mockService), "GetShouldersAndTagsAndTabsByCourseIds",
		func(cs *courseRecord2.ConfigCourseRecordService, ctx *gin.Context, courseList []dal.CourseInfo, towerCourseMap map[int64]baseTower.GetCourseInfoRsp) (keepDetail.CourseSchemaResponse, error) {
			mockResponse := keepDetail.CourseSchemaResponse{
				p.CourseId: keepDetail.CourseSchema{
					Shoulders:    []string{},
					ShoulderTags: []string{},
					TabData: map[string]keepDetail.TabHeaders{
						"coreData": {
							Headers: []keepDetail.Header{
								{
									Value:      "lessonName",
									Label:      "章节名称",
									Width:      "150",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "lpclessonName",
									Label:      "章节名称",
									Width:      "150",
									Hover:      "",
									Remark:     "原Lpc",
									CustomName: "",
								},
								{
									Value:      "startTime",
									Label:      "上课时间",
									Width:      "150",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "littleKidFudaoInteract",
									Label:      "互动题对答总",
									Width:      "100",
									Hover:      "",
									Remark:     "原小鹿辅导",
									CustomName: "",
								},
								{
									Value:      "littleKidFudaoHomeworkLevel",
									Label:      "作业成绩",
									Width:      "100",
									Hover:      "",
									Remark:     "原小鹿辅导",
									CustomName: "",
								},
								{
									Value:      "littleKidFudaoHomeworkStatus",
									Label:      "作业提交状态",
									Width:      "100",
									Hover:      "",
									Remark:     "原小鹿辅导",
									CustomName: "",
								},
								{
									Value:      "preview",
									Label:      "预习",
									Width:      "100",
									Hover:      "普通课程：展现学员预习作答正确数、作答数、试卷题目总数信息。ilab课程：展现学员预习成绩，分为优秀、良好、一般三类评级。",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "attend",
									Label:      "到课",
									Width:      "100",
									Hover:      "学员若请假且未到课，则展现学员请假。若学员到课，则展示学员直播课环节中累计到课时长。",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "playback",
									Label:      "回放观看时长",
									Width:      "100",
									Hover:      "展示的是用户累计观看回放时长，前14天实时更新，14天之后的数据每日更新一次",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "playbackv1",
									Label:      "回放观看时长(新)",
									Width:      "100",
									Hover:      "展示的是回放或LBP的累计观看时长。前60天实时更新，60天之后的数据每日更新一次",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "lbpAttendDuration",
									Label:      "录播内容观看时长",
									Width:      "100",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "lbpAttendDurationOld",
									Label:      "录播内容观看时长[旧]",
									Width:      "100",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "playbackOnlineTime",
									Label:      "回放在线时长",
									Width:      "100",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "inclassTest",
									Label:      "堂堂测",
									Width:      "100",
									Hover:      "普通课程：展现学员堂堂测作答正确数、作答数、试卷题目总数信息。ilab课程：展现学员堂堂测成绩，分为优秀、良好、一般三类评级。",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "oralQuestion",
									Label:      "口述题",
									Width:      "100",
									Hover:      "展现学员口述题提交状态，分为已提交、未提交两种",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "homework",
									Label:      "巩固练习",
									Width:      "100",
									Hover:      "普通课程：展现学员巩固练习作答评级，分为S\\/A\\/B三类等级。ilab课程：展现学员巩固练习成绩，分为优秀、良好、一般三类评级。",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "similarHomework",
									Label:      "相似题",
									Width:      "100",
									Hover:      "展现学员错题再练相似题作答评级，分为S\\/A\\/B三类等级",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "exercise",
									Label:      "直播互动题",
									Width:      "100",
									Hover:      "展现学员直播过程中参与的互动题作答正确数、作答数、试卷题目总数信息。",
									Remark:     "通用",
									CustomName: "",
								},
								{
									Value:      "exerciseAll",
									Label:      "观看互动题",
									Width:      "100",
									Hover:      " 展现学员直播+回放过程中参与的互动题作答正确数、作答数、试卷题目总数信息。",
									Remark:     "通用",
									CustomName: "",
								},
								{
									Value:      "lbpInteractExam",
									Label:      "录播互动题",
									Width:      "100",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "mixPlaybackInteract",
									Label:      "融合回放互动题",
									Width:      "100",
									Hover:      "展现学员在融合直播间回放过程中参与的互动题作答正确数、作答数、试卷题目总数信息。",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "synchronousPractice",
									Label:      "同步练习",
									Width:      "100",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "hasCompositionReport",
									Label:      "作文报告",
									Width:      "100",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "praise",
									Label:      "表扬",
									Width:      "100",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "isInclassTeacherRoomAttend30minute",
									Label:      "\"到课情况\"（直播章节）",
									Width:      "100",
									Hover:      "",
									Remark:     "辅导",
									CustomName: "",
								},
								{
									Value:      "isAttendFinish",
									Label:      "\"完课情况\"（直播章节）",
									Width:      "100",
									Hover:      "",
									Remark:     "辅导",
									CustomName: "",
								},
								{
									Value:      "gjkAttendLessonLubo",
									Label:      "\"到课情况\"（录播章节）",
									Width:      "100",
									Hover:      "",
									Remark:     "辅导",
									CustomName: "",
								},
								{
									Value:      "gjkCompleteLessonLubo",
									Label:      "\"完课情况\"（录播章节）",
									Width:      "100",
									Hover:      "",
									Remark:     "辅导",
									CustomName: "",
								},
								{
									Value:      "gjkLessonTag",
									Label:      "必看章节",
									Width:      "100",
									Hover:      "",
									Remark:     "辅导",
									CustomName: "",
								},
								{
									Value:      "microphone",
									Label:      "抢麦",
									Width:      "100",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "talk",
									Label:      "课中聊天",
									Width:      "100",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "score",
									Label:      "学分",
									Width:      "100",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "rewardNum",
									Label:      "星星数",
									Width:      "100",
									Hover:      "展现学员各环节完成后获得的星星数。",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "monthlyExamReport",
									Label:      "考试报告",
									Width:      "100",
									Hover:      "需在学员提交堂堂测之后才会生成考试报告哦。",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "teacherName",
									Label:      "主讲",
									Width:      "100",
									Hover:      "",
									Remark:     "原Lpc",
									CustomName: "",
								},
								{
									Value:      "attendStatus",
									Label:      "到课",
									Width:      "100",
									Hover:      "",
									Remark:     "原Lpc",
									CustomName: "",
								},
								{
									Value:      "finishStatus",
									Label:      "完课",
									Width:      "100",
									Hover:      "",
									Remark:     "原Lpc",
									CustomName: "",
								},
								{
									Value:      "playStatus",
									Label:      "回放",
									Width:      "100",
									Hover:      "",
									Remark:     "原Lpc",
									CustomName: "",
								},
								{
									Value:      "preView",
									Label:      "预习（对\\/答\\/总）",
									Width:      "100",
									Hover:      "",
									Remark:     "原Lpc",
									CustomName: "",
								},
								{
									Value:      "tangtangExamStat",
									Label:      "堂堂测（对\\/答\\/总）",
									Width:      "100",
									Hover:      "",
									Remark:     "原Lpc",
									CustomName: "",
								},
								{
									Value:      "strengthPracticeStatus",
									Label:      "巩固练习",
									Width:      "100",
									Hover:      "",
									Remark:     "原Lpc",
									CustomName: "",
								},
								{
									Value:      "lessonReportUrl",
									Label:      "课堂报告",
									Width:      "100",
									Hover:      "",
									Remark:     "原Lpc",
									CustomName: "",
								},
								{
									Value:      "deerEloquenceHomeworkLevel",
									Label:      "小鹿口才作业状态",
									Width:      "100",
									Hover:      "",
									Remark:     "原小鹿",
									CustomName: "",
								},
								{
									Value:      "deerProgrammingHomeworkLevel",
									Label:      "小鹿编程作业状态",
									Width:      "100",
									Hover:      "",
									Remark:     "原小鹿",
									CustomName: "",
								},
								{
									Value:      "deerLessonReportUrl",
									Label:      "ai课章节报告",
									Width:      "100",
									Hover:      "",
									Remark:     "小鹿使用",
									CustomName: "",
								},
								{
									Value:      "deerLessonHomeWork",
									Label:      "ai课章节作品",
									Width:      "100",
									Hover:      "",
									Remark:     "小鹿使用",
									CustomName: "",
								},
								{
									Value:      "zhiboLessonReportUrl",
									Label:      "直播课章节报告",
									Width:      "100",
									Hover:      "",
									Remark:     "直播课使用",
									CustomName: "",
								},
								{
									Value:      "deerAIContentTime",
									Label:      "ai课观看时间",
									Width:      "100",
									Hover:      "",
									Remark:     "ai课观看时长",
									CustomName: "",
								},
								{
									Value:      "isAIAttendLesson",
									Label:      "ai课是否到课",
									Width:      "100",
									Hover:      "",
									Remark:     "ai课是否到课",
									CustomName: "",
								},
								{
									Value:      "isAILessonOverclass",
									Label:      "ai课是否完课",
									Width:      "100",
									Hover:      "",
									Remark:     "ai课是否完课",
									CustomName: "",
								},
								{
									Value:      "deerLpcBcHomeworkStatus",
									Label:      "小鹿编程作业提交状态",
									Width:      "100",
									Hover:      "",
									Remark:     "小鹿编程作业提交状态",
									CustomName: "",
								},
								{
									Value:      "inclass_teacher_room_total_playback_content_time",
									Label:      "ai课观看时长",
									Width:      "100",
									Hover:      "",
									Remark:     "录播\\/回放内容观看时长",
									CustomName: "ai课观看时长",
								},
								{
									Value:      "is_inclass_teacher_room_content_view_10s",
									Label:      "ai课是否到课",
									Width:      "100",
									Hover:      "",
									Remark:     "ai课是否到课",
									CustomName: "ai课是否到课",
								},
								{
									Value:      "is_inclass_teacher_room_content_view_finish_85percent",
									Label:      "ai课是否完课",
									Width:      "100",
									Hover:      "",
									Remark:     "ai课是否完课",
									CustomName: "ai课是否完课",
								},
								{
									Value:      "isOverallFinish",
									Label:      "ai课是否整体完课",
									Width:      "100",
									Hover:      "",
									Remark:     "ai课是否整体完课",
									CustomName: "ai课是否整体完课",
								},
								{
									Value:      "playContentTime",
									Label:      "ai课观看时长",
									Width:      "100",
									Hover:      "",
									Remark:     "ai课观看时长",
									CustomName: "ai课观看时长",
								},
								{
									Value:      "contractPlayContentTime",
									Label:      "ai课观看时长(合约)",
									Width:      "100",
									Hover:      "",
									Remark:     "原小鹿权益包。ai课看内容时长",
									CustomName: "ai课观看时长(合约)",
								},
								{
									Value:      "is_contract_inclass_teacher_room_content_view_10s_contract",
									Label:      "ai课是否到课(合约)",
									Width:      "100",
									Hover:      "",
									Remark:     "原小鹿权益包，ai课时长大于10s为已到课",
									CustomName: "ai课是否到课(合约)",
								},
								{
									Value:      "is_contract_inclass_teacher_room_content_view_finish_85percent_contract",
									Label:      "ai课是否完课(合约)",
									Width:      "100",
									Hover:      "",
									Remark:     "原小鹿权益包，内容时长>=85%为已完课",
									CustomName: "ai课是否完课(合约)",
								},
								{
									Value:      "isContractOverallFinish",
									Label:      "ai课是否整体完课(合约)",
									Width:      "100",
									Hover:      "",
									Remark:     "原小鹿权益包，ai课已完成且提交作品为已整体完课",
									CustomName: "ai课是否整体完课(合约)",
								},
								{
									Value:      "is_contract_inclass_teacher_room_attend_5minute",
									Label:      "直播是否到课",
									Width:      "100",
									Hover:      "",
									Remark:     "小鹿写字课程可用，直播到课>5分钟时已到课",
									CustomName: "直播是否到课",
								},
								{
									Value:      "is_contract_inclass_teacher_room_attend_finish",
									Label:      "直播是否完课(合约)",
									Width:      "100",
									Hover:      "",
									Remark:     "小鹿写字课程可用，直播到课>3\\/4内容时已完课",
									CustomName: "直播是否完课(合约)",
								},
								{
									Value:      "isContractInclassTeacherRoomContentView5Mintue",
									Label:      "录播\\/回放是否观看到课_5min(合约)",
									Width:      "100",
									Hover:      "",
									Remark:     "小鹿写字课程可用，直播+回放内容时长>5分钟时为回放到课",
									CustomName: "录播\\/回放是否观看到课_5min(合约)",
								},
								{
									Value:      "is_contract_inclass_teacher_room_content_view_finish_three_four",
									Label:      "直播回放是否完课",
									Width:      "100",
									Hover:      "",
									Remark:     "小鹿写字课程可用，直播+回放内容时长>3\\/4内容时为回放完课",
									CustomName: "直播回放是否完课",
								},
								{
									Value:      "inclassDuration",
									Label:      "内容观看时长",
									Width:      "100",
									Hover:      "",
									Remark:     "内容观看时长",
									CustomName: "内容观看时长",
								},
								{
									Value:      "inclassHasFinish",
									Label:      "内容观看完课状态",
									Width:      "100",
									Hover:      "",
									Remark:     "内容观看完课状态",
									CustomName: "内容观看完课状态",
								},
								{
									Value:      "lbpPlayContentTime",
									Label:      "录播内容观看时长（新）",
									Width:      "100",
									Hover:      "",
									Remark:     "录播内容观看时长（新）",
									CustomName: "录播内容观看时长（新）",
								},
								{
									Value:      "preciseExercisesStatus",
									Label:      "精准练习状态",
									Width:      "100",
									Hover:      "",
									Remark:     "精准练习状态",
									CustomName: "精准练习状态",
								},
								{
									Value:      "inclassTeacherRoomAttendDuration",
									Label:      "【M-ABC】直播观看时长",
									Width:      "100",
									Hover:      "",
									Remark:     "【M-ABC】直播观看时长",
									CustomName: "【M-ABC】直播观看时长",
								},
								{
									Value:      "inclassTeacherRoomTotalPlaybackContentTime",
									Label:      "【M-ABC】回放内容观看时长",
									Width:      "100",
									Hover:      "",
									Remark:     "【M-ABC】回放内容观看时长",
									CustomName: "【M-ABC】回放内容观看时长",
								},
								{
									Value:      "isInclassTeacherRoomAttendOrContentViewThreeFour",
									Label:      "【M-ABC】观看完课状态",
									Width:      "100",
									Hover:      "",
									Remark:     "【M-ABC】观看完课状态",
									CustomName: "【M-ABC】观看完课状态",
								},
								{
									Value:      "isInclassTeacherRoomAttendOrContentView30minute",
									Label:      "【M-ABC】观看到课状态",
									Width:      "100",
									Hover:      "",
									Remark:     "【M-ABC】观看到课状态",
									CustomName: "【M-ABC】观看到课状态",
								},
								{
									Value:      "isInclassTeacherRoomAttendFinish",
									Label:      "【M-ABC】是否直播完课",
									Width:      "100",
									Hover:      "",
									Remark:     "【M-ABC】是否直播完课",
									CustomName: "【M-ABC】是否直播完课",
								},
								{
									Value:      "isInclassTeacherRoomAttend30minute",
									Label:      "【M-ABC】是否直播到课",
									Width:      "100",
									Hover:      "",
									Remark:     "【M-ABC】是否直播到课",
									CustomName: "【M-ABC】是否直播到课",
								},
								{
									Value:      "interactiveCnt",
									Label:      "【M-ABC】直播互动题对答总",
									Width:      "100",
									Hover:      "",
									Remark:     "【M-ABC】直播互动题对答总",
									CustomName: "【M-ABC】直播互动题对答总",
								},
								{
									Value:      "inclassMultyLinkCnt",
									Label:      "【M-ABC】视频连麦次数",
									Width:      "100",
									Hover:      "",
									Remark:     "【M-ABC】视频连麦次数",
									CustomName: "【M-ABC】视频连麦次数",
								},
								{
									Value:      "inclassChatCnt",
									Label:      "【M-ABC】课中直播间聊天次数",
									Width:      "100",
									Hover:      "",
									Remark:     "【M-ABC】课中直播间聊天次数",
									CustomName: "【M-ABC】课中直播间聊天次数",
								},
								{
									Value:      "isAllLiveLessonViewFinish",
									Label:      "【大阅读】本单元观看完课达标",
									Width:      "100",
									Hover:      "",
									Remark:     "【大阅读】本单元观看完课达标",
									CustomName: "【大阅读】本单元观看完课达标",
								},
								{
									Value:      "isAllLiveLessonViewAttend",
									Label:      "【大阅读】本单元观看到课达标",
									Width:      "100",
									Hover:      "",
									Remark:     "【大阅读】本单元观看到课达标",
									CustomName: "【大阅读】本单元观看到课达标",
								},
								{
									Value:      "isAllLiveLessonFinish",
									Label:      "【大阅读】本单元直播完课达标",
									Width:      "100",
									Hover:      "",
									Remark:     "【大阅读】本单元直播完课达标",
									CustomName: "【大阅读】本单元直播完课达标",
								},
								{
									Value:      "isAllLiveLessonAttend",
									Label:      "【大阅读】本单元直播到课达标",
									Width:      "100",
									Hover:      "",
									Remark:     "【大阅读】本单元直播到课达标",
									CustomName: "【大阅读】本单元直播到课达标",
								},
								{
									Value:      "inclassTeacherRoomContentViewOneThird",
									Label:      "【大阅读】LBP录播是否观看到课",
									Width:      "100",
									Hover:      "",
									Remark:     "【大阅读】LBP录播是否观看到课",
									CustomName: "【大阅读】LBP录播是否观看到课",
								},
								{
									Value:      "inclassTeacherRoomZpluspContentViewThreeFour",
									Label:      "【大阅读】观看完课状态",
									Width:      "100",
									Hover:      "",
									Remark:     "【大阅读】观看完课状态",
									CustomName: "【大阅读】观看完课状态",
								},
								{
									Value:      "inclassTeacherRoomAttendOrContentViewOneThird",
									Label:      "【大阅读】观看到课状态",
									Width:      "100",
									Hover:      "",
									Remark:     "【大阅读】观看到课状态",
									CustomName: "【大阅读】观看到课状态",
								},
								{
									Value:      "inclassTeacherRoomAttendOneThird",
									Label:      "【大阅读】是否直播到课",
									Width:      "100",
									Hover:      "",
									Remark:     "【大阅读】是否直播到课",
									CustomName: "【大阅读】是否直播到课",
								},
								{
									Value:      "beforeclassAssistantRoomAttend",
									Label:      "【大阅读】课前出镜是否到课",
									Width:      "100",
									Hover:      "",
									Remark:     "【大阅读】课前出镜是否到课",
									CustomName: "【大阅读】课前出镜是否到课",
								},
								{
									Value:      "beforeclassAssistantRoomAttendFinish",
									Label:      "【大阅读】课前出镜是否完课",
									Width:      "100",
									Hover:      "",
									Remark:     "【大阅读】课前出镜是否完课",
									CustomName: "【大阅读】课前出镜是否完课",
								},
								{
									Value:      "preciseExercisesCorrectLevel",
									Label:      "精准练习评级",
									Width:      "100",
									Hover:      "",
									Remark:     "preciseExercisesCorrectLevel",
									CustomName: "精准练习评级",
								},
							},
						},
					},
					SchemaID: 43,
				},
			}
			return mockResponse, nil
		})
	defer patch2.Unpatch()
	defer patch3.Unpatch()

	// 执行被测试的方法
	service := performanceService{}
	resp, err := service.GetPerformanceV1(ctx, p)

	// 输出调试信息
	components.DebugfWithJSON(ctx, "GetPerformanceV1 resp: %s", resp)

	// 断言测试结果
	assert.Nil(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "43", resp.SchemaId)
	assert.NotNil(t, resp.TableHeader)
	assert.NotNil(t, resp.TableData)

	// === 新增：调用PHP版本接口进行数据对比 ===
	zlog.Infof(ctx, "=== 开始调用PHP版本接口进行数据对比 ===")

	// 创建PHP接口客户端
	phpClient := assistantdesk.NewClient()

	// 构建PHP接口请求参数
	phpRequest := assistantdesk.PerformanceV1Request{
		StudentUid:   p.StudentUid,
		PersonUid:    p.PersonUid,
		CourseId:     p.CourseId,
		LeadsId:      p.LeadsId,
		Tab:          p.Tab,
		AssistantUid: p.AssistantUid,
		IsExport:     p.IsExport,
	}

	// 调用PHP接口
	phpResp, err := phpClient.GetPerformanceV1(ctx, phpRequest)
	if err != nil {
		zlog.Errorf(ctx, "调用PHP接口失败: %v", err)
		// 不影响原有测试，只记录错误
		t.Logf("警告：PHP接口调用失败，跳过数据对比: %v", err)
		return
	}

	zlog.Infof(ctx, "PHP接口调用成功")

	// 转换Go版本数据为map格式以便对比
	goDataMap := map[string][]map[string]interface{}{
		"tableData":   resp.TableData,
		"tableHeader": convertTableHeaderToInterface(resp.TableHeader),
	}

	// 转换PHP版本数据为map格式
	phpDataMap := map[string][]map[string]interface{}{
		"tableData":   phpResp.TableData,
		"tableHeader": phpResp.TableHeader,
	}

	newComparisonResult := utils.ComparePerformanceV1DataNew(ctx, phpDataMap, goDataMap)

	// 输出对比结果
	newResultJson, _ := json.MarshalIndent(newComparisonResult, "", "  ")
	fmt.Printf("对比结果:\n%s\n", string(newResultJson))
}

func TestPerformanceService_GetPerformanceV1_Exam(t *testing.T) {
	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx := createCtx()

	// 设置输入参数
	p := &inputStudent.PerformanceV1Param{
		StudentUid:   2285235952,
		CourseId:     3067958,
		LeadsId:      1561215123,
		Tab:          consts.TAB_EXAM,
		AssistantUid: 4312624260,
		PersonUid:    2136917703,
		IsExport:     0,
	}

	// 执行被测试的方法
	service := performanceService{}
	resp, err := service.GetPerformanceV1(ctx, p)

	// 输出调试信息
	components.DebugfWithJSON(ctx, "GetPerformanceV1 TableData: %s", resp.TableData)
	components.DebugfWithJSON(ctx, "GetPerformanceV1 TableHeader: %s", resp.TableHeader)

	// 断言测试结果
	assert.Nil(t, err)
	assert.NotNil(t, resp)
	assert.NotNil(t, resp.TableHeader)
	assert.NotNil(t, resp.TableData)
}

// convertTableHeaderToInterface 将Go版本的TableHeader转换为interface{}格式
func convertTableHeaderToInterface(tableHeader []outputStudent.TableHeaderItem) []map[string]interface{} {
	result := make([]map[string]interface{}, len(tableHeader))
	for i, header := range tableHeader {
		result[i], _ = util.StructToMap(header)
	}
	return result
}
