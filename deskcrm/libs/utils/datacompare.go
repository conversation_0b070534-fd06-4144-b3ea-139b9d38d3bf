package utils

import (
	"fmt"
	"reflect"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// NewComparisonResult 新的对比结果结构，按照要求的输出格式
type NewComparisonResult struct {
	TableData   []TableDataDiff   `json:"tableData"`
	TableHeader []TableHeaderDiff `json:"tableHeader"`
}

// TableDataDiff tableData的差异结构
type TableDataDiff struct {
	LessonId int64       `json:"lessonId"`
	Diff     []FieldDiff `json:"diff"`
}

// TableHeaderDiff tableHeader的差异结构
type TableHeaderDiff struct {
	Prop string      `json:"prop"`
	Diff []FieldDiff `json:"diff"`
}

// FieldDiff 字段差异详情
type FieldDiff struct {
	Field string      `json:"field"`
	PHP   interface{} `json:"php"`
	Go    interface{} `json:"go"`
}

// 对于tableData，以lessonId为主键进行匹配，然后对比其余的key
// 对于tableHeader，以prop为主键进行匹配，然后对比其余的key
// 以PHP为基准对比Go版本的数据，只考虑PHP中出现的字段
func ComparePerformanceV1DataNew(ctx *gin.Context, phpData, goData map[string][]map[string]interface{}) *NewComparisonResult {
	result := &NewComparisonResult{
		TableData:   []TableDataDiff{},
		TableHeader: []TableHeaderDiff{},
	}

	zlog.Infof(ctx, "开始新格式数据对比，以PHP版本为基准")

	// 对比 tableData
	if phpTableData, exists := phpData["tableData"]; exists {
		goTableData := goData["tableData"]
		tableDataDiffs := compareTableDataNew(ctx, phpTableData, goTableData)
		result.TableData = tableDataDiffs
	}

	// 对比 tableHeader
	if phpTableHeader, exists := phpData["tableHeader"]; exists {
		goTableHeader := goData["tableHeader"]
		tableHeaderDiffs := compareTableHeaderNew(ctx, phpTableHeader, goTableHeader)
		result.TableHeader = tableHeaderDiffs
	}

	zlog.Infof(ctx, "新格式数据对比完成: tableData差异=%d, tableHeader差异=%d",
		len(result.TableData), len(result.TableHeader))

	return result
}

// compareTableDataNew 对比tableData数组，以lessonId为主键匹配
func compareTableDataNew(ctx *gin.Context, phpData, goData []map[string]interface{}) []TableDataDiff {
	var diffs []TableDataDiff

	// 创建Go数据的lessonId索引
	goIndexMap := make(map[int64]map[string]interface{})
	for _, goItem := range goData {
		if lessonId, exists := goItem["lessonId"]; exists {
			goIndexMap[cast.ToInt64(lessonId)] = goItem
		}
	}

	// 遍历PHP数据，以lessonId为主键进行匹配
	for _, phpItem := range phpData {
		lessonId, lessonIdExists := phpItem["lessonId"]
		if !lessonIdExists {
			continue
		}

		diff := TableDataDiff{
			LessonId: cast.ToInt64(lessonId),
			Diff:     []FieldDiff{},
		}

		// 查找对应的Go记录
		goMap, goExists := goIndexMap[cast.ToInt64(lessonId)]
		if !goExists {
			// Go中没有对应的lessonId记录
			for field, phpValue := range phpItem {
				if field != "lessonId" { // 排除主键字段
					diff.Diff = append(diff.Diff, FieldDiff{
						Field: field,
						PHP:   phpValue,
						Go:    nil,
					})
				}
			}
		} else {
			// 对比除lessonId外的其他字段
			for field, phpValue := range phpItem {
				if field != "lessonId" { // 排除主键字段
					goValue, goFieldExists := goMap[field]
					if !goFieldExists {
						// Go中缺失该字段
						diff.Diff = append(diff.Diff, FieldDiff{
							Field: field,
							PHP:   phpValue,
							Go:    nil,
						})
					} else if !valuesEqual(phpValue, goValue) {
						// 字段值不同
						diff.Diff = append(diff.Diff, FieldDiff{
							Field: field,
							PHP:   phpValue,
							Go:    goValue,
						})
					}
				}
			}
		}

		// 只有存在差异时才添加到结果中
		if len(diff.Diff) > 0 {
			diffs = append(diffs, diff)
		}
	}

	return diffs
}

// compareTableHeaderNew 对比tableHeader数组，以prop为主键匹配
func compareTableHeaderNew(ctx *gin.Context, phpData, goData []map[string]interface{}) []TableHeaderDiff {
	var diffs []TableHeaderDiff

	// 创建Go数据的prop索引
	goIndexMap := make(map[string]map[string]interface{})
	for _, goItem := range goData {
		if prop, exists := goItem["prop"]; exists {
			if propStr, ok := prop.(string); ok {
				goIndexMap[propStr] = goItem
			}
		}
	}

	// 遍历PHP数据，以prop为主键进行匹配
	for _, phpItem := range phpData {
		prop, propExists := phpItem["prop"]
		if !propExists {
			continue
		}

		propStr, propOk := prop.(string)
		if !propOk {
			continue
		}

		diff := TableHeaderDiff{
			Prop: propStr,
			Diff: []FieldDiff{},
		}

		// 查找对应的Go记录
		goMap, goExists := goIndexMap[propStr]
		if !goExists {
			// Go中没有对应的prop记录
			for field, phpValue := range phpItem {
				if field != "prop" { // 排除主键字段
					diff.Diff = append(diff.Diff, FieldDiff{
						Field: field,
						PHP:   phpValue,
						Go:    nil,
					})
				}
			}
		} else {
			// 对比除prop外的其他字段
			for field, phpValue := range phpItem {
				if field != "prop" { // 排除主键字段
					goValue, goFieldExists := goMap[field]
					if !goFieldExists {
						// Go中缺失该字段
						diff.Diff = append(diff.Diff, FieldDiff{
							Field: field,
							PHP:   phpValue,
							Go:    nil,
						})
					} else if !valuesEqual(phpValue, goValue) {
						// 字段值不同
						diff.Diff = append(diff.Diff, FieldDiff{
							Field: field,
							PHP:   phpValue,
							Go:    goValue,
						})
					}
				}
			}
		}

		// 只有存在差异时才添加到结果中
		if len(diff.Diff) > 0 {
			diffs = append(diffs, diff)
		}
	}

	return diffs
}

// valuesEqual 比较两个值是否相等，支持类型转换
func valuesEqual(a, b interface{}) bool {
	// 直接比较
	if reflect.DeepEqual(a, b) {
		return true
	}

	// 尝试标准化后比较（处理类型转换）
	return normalizedEqual(a, b)
}

// normalizedEqual 标准化后比较（处理类型转换）
func normalizedEqual(a, b interface{}) bool {
	// 处理字符串和数字的转换
	aStr := fmt.Sprintf("%v", a)
	bStr := fmt.Sprintf("%v", b)
	return aStr == bStr
}
