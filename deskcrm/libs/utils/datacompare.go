package utils

import (
	"fmt"
	"reflect"
	"sort"
	"strings"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// ComparisonResult 数据对比结果
type ComparisonResult struct {
	IsEqual     bool                   `json:"isEqual"`     // 是否完全相等
	Differences []FieldDifference      `json:"differences"` // 差异列表
	Summary     ComparisonSummary      `json:"summary"`     // 对比摘要
	Details     map[string]interface{} `json:"details"`     // 详细信息
}

// FieldDifference 字段差异
type FieldDifference struct {
	Path        string      `json:"path"`        // 字段路径，如 "tableData[0].lessonName"
	PHPValue    interface{} `json:"phpValue"`    // PHP版本的值
	GoValue     interface{} `json:"goValue"`     // Go版本的值
	DiffType    string      `json:"diffType"`    // 差异类型：missing_in_go, missing_in_php, value_diff
	Description string      `json:"description"` // 差异描述
}

// ComparisonSummary 对比摘要
type ComparisonSummary struct {
	TotalFields      int `json:"totalFields"`      // PHP版本总字段数
	MatchedFields    int `json:"matchedFields"`    // 匹配的字段数
	MissingInGo      int `json:"missingInGo"`      // Go版本中缺失的字段数
	MissingInPHP     int `json:"missingInPHP"`     // PHP版本中缺失的字段数（理论上为0，因为以PHP为基准）
	ValueDifferences int `json:"valueDifferences"` // 值不同的字段数
	TableDataDiffs   int `json:"tableDataDiffs"`   // tableData差异数
	TableHeaderDiffs int `json:"tableHeaderDiffs"` // tableHeader差异数
}

// ComparePerformanceV1Data 对比PerformanceV1接口的数据
// 以PHP版本为基准，检查Go版本中是否有对应的key以及value是否相等
func ComparePerformanceV1Data(ctx *gin.Context, phpData, goData map[string]interface{}) *ComparisonResult {
	result := &ComparisonResult{
		IsEqual:     true,
		Differences: []FieldDifference{},
		Summary:     ComparisonSummary{},
		Details:     make(map[string]interface{}),
	}

	zlog.Infof(ctx, "开始对比PerformanceV1数据，以PHP版本为基准")

	// 对比 tableData
	if phpTableData, exists := phpData["tableData"]; exists {
		goTableData := goData["tableData"]
		tableDataDiffs := compareTableData(ctx, phpTableData, goTableData, "tableData")
		result.Differences = append(result.Differences, tableDataDiffs...)
		result.Summary.TableDataDiffs = len(tableDataDiffs)
	}

	// 对比 tableHeader
	if phpTableHeader, exists := phpData["tableHeader"]; exists {
		goTableHeader := goData["tableHeader"]
		tableHeaderDiffs := compareTableHeader(ctx, phpTableHeader, goTableHeader, "tableHeader")
		result.Differences = append(result.Differences, tableHeaderDiffs...)
		result.Summary.TableHeaderDiffs = len(tableHeaderDiffs)
	}

	// 对比 schemaId
	if phpSchemaId, exists := phpData["schemaId"]; exists {
		goSchemaId := goData["schemaId"]
		if schemaDiff := compareValues("schemaId", phpSchemaId, goSchemaId); schemaDiff != nil {
			result.Differences = append(result.Differences, *schemaDiff)
		}
	}

	// 计算摘要
	result.Summary.TotalFields = countTotalFields(phpData)
	for _, diff := range result.Differences {
		switch diff.DiffType {
		case "missing_in_go":
			result.Summary.MissingInGo++
		case "missing_in_php":
			result.Summary.MissingInPHP++
		case "value_diff":
			result.Summary.ValueDifferences++
		}
	}
	result.Summary.MatchedFields = result.Summary.TotalFields - result.Summary.MissingInGo - result.Summary.ValueDifferences

	// 判断是否完全相等
	result.IsEqual = len(result.Differences) == 0

	// 添加详细信息
	result.Details["phpDataKeys"] = getMapKeys(phpData)
	result.Details["goDataKeys"] = getMapKeys(goData)
	result.Details["comparisonTime"] = getCurrentTimestamp()

	zlog.Infof(ctx, "数据对比完成: 总字段=%d, 匹配=%d, Go缺失=%d, 值差异=%d, 是否相等=%v",
		result.Summary.TotalFields, result.Summary.MatchedFields,
		result.Summary.MissingInGo, result.Summary.ValueDifferences, result.IsEqual)

	return result
}

// compareTableData 对比tableData数组
func compareTableData(ctx *gin.Context, phpData, goData interface{}, basePath string) []FieldDifference {
	var diffs []FieldDifference

	phpArray, phpOk := phpData.([]interface{})
	goArray, goOk := goData.([]interface{})

	if !phpOk {
		// PHP数据不是数组格式
		if phpData != nil {
			diffs = append(diffs, FieldDifference{
				Path:        basePath,
				PHPValue:    phpData,
				GoValue:     goData,
				DiffType:    "type_mismatch",
				Description: "PHP数据不是数组格式",
			})
		}
		return diffs
	}

	if !goOk {
		// Go数据不是数组格式或为nil
		diffs = append(diffs, FieldDifference{
			Path:        basePath,
			PHPValue:    phpData,
			GoValue:     goData,
			DiffType:    "missing_in_go",
			Description: "Go版本中tableData缺失或格式不正确",
		})
		return diffs
	}

	// 对比数组长度
	if len(phpArray) != len(goArray) {
		diffs = append(diffs, FieldDifference{
			Path:        basePath + ".length",
			PHPValue:    len(phpArray),
			GoValue:     len(goArray),
			DiffType:    "value_diff",
			Description: fmt.Sprintf("数组长度不同: PHP=%d, Go=%d", len(phpArray), len(goArray)),
		})
	}

	// 对比每个元素
	maxLen := len(phpArray)
	if len(goArray) > maxLen {
		maxLen = len(goArray)
	}

	for i := 0; i < maxLen; i++ {
		itemPath := fmt.Sprintf("%s[%d]", basePath, i)

		if i >= len(phpArray) {
			// PHP数组中没有这个索引
			diffs = append(diffs, FieldDifference{
				Path:        itemPath,
				PHPValue:    nil,
				GoValue:     goArray[i],
				DiffType:    "missing_in_php",
				Description: "Go版本中存在额外的数组元素",
			})
			continue
		}

		if i >= len(goArray) {
			// Go数组中没有这个索引
			diffs = append(diffs, FieldDifference{
				Path:        itemPath,
				PHPValue:    phpArray[i],
				GoValue:     nil,
				DiffType:    "missing_in_go",
				Description: "Go版本中缺失数组元素",
			})
			continue
		}

		// 对比对象内容
		itemDiffs := compareMapObjects(phpArray[i], goArray[i], itemPath)
		diffs = append(diffs, itemDiffs...)
	}

	return diffs
}

// compareTableHeader 对比tableHeader数组
func compareTableHeader(ctx *gin.Context, phpData, goData interface{}, basePath string) []FieldDifference {
	// tableHeader的对比逻辑与tableData类似
	return compareTableData(ctx, phpData, goData, basePath)
}

// compareMapObjects 对比两个map对象
func compareMapObjects(phpObj, goObj interface{}, basePath string) []FieldDifference {
	var diffs []FieldDifference

	phpMap, phpOk := phpObj.(map[string]interface{})
	goMap, goOk := goObj.(map[string]interface{})

	if !phpOk {
		if phpObj != nil {
			diffs = append(diffs, FieldDifference{
				Path:        basePath,
				PHPValue:    phpObj,
				GoValue:     goObj,
				DiffType:    "type_mismatch",
				Description: "PHP数据不是对象格式",
			})
		}
		return diffs
	}

	if !goOk {
		diffs = append(diffs, FieldDifference{
			Path:        basePath,
			PHPValue:    phpObj,
			GoValue:     goObj,
			DiffType:    "missing_in_go",
			Description: "Go版本中对象缺失或格式不正确",
		})
		return diffs
	}

	// 以PHP版本为基准，检查每个字段
	for key, phpValue := range phpMap {
		fieldPath := basePath + "." + key
		goValue, exists := goMap[key]

		if !exists {
			// Go版本中缺失该字段
			diffs = append(diffs, FieldDifference{
				Path:        fieldPath,
				PHPValue:    phpValue,
				GoValue:     nil,
				DiffType:    "missing_in_go",
				Description: fmt.Sprintf("Go版本中缺失字段: %s", key),
			})
			continue
		}

		// 对比字段值
		if fieldDiff := compareValues(fieldPath, phpValue, goValue); fieldDiff != nil {
			diffs = append(diffs, *fieldDiff)
		}
	}

	return diffs
}

// compareValues 对比两个值
func compareValues(path string, phpValue, goValue interface{}) *FieldDifference {
	if reflect.DeepEqual(phpValue, goValue) {
		return nil
	}

	// 尝试类型转换后再比较
	if normalizedEqual(phpValue, goValue) {
		return nil
	}

	return &FieldDifference{
		Path:        path,
		PHPValue:    phpValue,
		GoValue:     goValue,
		DiffType:    "value_diff",
		Description: fmt.Sprintf("值不同: PHP=%v, Go=%v", phpValue, goValue),
	}
}

// normalizedEqual 标准化后比较（处理类型转换）
func normalizedEqual(a, b interface{}) bool {
	// 处理字符串和数字的转换
	aStr := fmt.Sprintf("%v", a)
	bStr := fmt.Sprintf("%v", b)
	return aStr == bStr
}

// countTotalFields 计算总字段数
func countTotalFields(data map[string]interface{}) int {
	count := 0
	for _, value := range data {
		count += countFieldsRecursive(value)
	}
	return count
}

// countFieldsRecursive 递归计算字段数
func countFieldsRecursive(value interface{}) int {
	switch v := value.(type) {
	case map[string]interface{}:
		count := len(v)
		for _, subValue := range v {
			count += countFieldsRecursive(subValue)
		}
		return count
	case []interface{}:
		count := 0
		for _, item := range v {
			count += countFieldsRecursive(item)
		}
		return count
	default:
		return 1
	}
}

// getMapKeys 获取map的所有key
func getMapKeys(data map[string]interface{}) []string {
	keys := make([]string, 0, len(data))
	for key := range data {
		keys = append(keys, key)
	}
	sort.Strings(keys)
	return keys
}

// getCurrentTimestamp 获取当前时间戳
func getCurrentTimestamp() int64 {
	return 0 // 简化实现，实际可以返回真实时间戳
}

// FormatComparisonResult 格式化对比结果为可读字符串
func FormatComparisonResult(result *ComparisonResult) string {
	var builder strings.Builder

	// 标题和总体结果
	builder.WriteString("╔══════════════════════════════════════════════════════════════╗\n")
	builder.WriteString("║                    PerformanceV1 数据对比结果                    ║\n")
	builder.WriteString("╚══════════════════════════════════════════════════════════════╝\n")

	// 总体状态
	if result.IsEqual {
		builder.WriteString("🎉 对比结果: ✅ 完全一致\n")
	} else {
		builder.WriteString("⚠️  对比结果: ❌ 发现差异\n")
	}

	// 统计信息
	builder.WriteString("\n📊 统计摘要:\n")
	builder.WriteString("┌─────────────────────────────────────────────────────────────┐\n")
	builder.WriteString(fmt.Sprintf("│ 总字段数:           %6d                                │\n", result.Summary.TotalFields))
	builder.WriteString(fmt.Sprintf("│ 匹配字段数:         %6d                                │\n", result.Summary.MatchedFields))
	builder.WriteString(fmt.Sprintf("│ Go版本缺失字段:     %6d                                │\n", result.Summary.MissingInGo))
	builder.WriteString(fmt.Sprintf("│ 值差异字段:         %6d                                │\n", result.Summary.ValueDifferences))
	builder.WriteString("├─────────────────────────────────────────────────────────────┤\n")
	builder.WriteString(fmt.Sprintf("│ TableData差异:      %6d                                │\n", result.Summary.TableDataDiffs))
	builder.WriteString(fmt.Sprintf("│ TableHeader差异:    %6d                                │\n", result.Summary.TableHeaderDiffs))
	builder.WriteString("└─────────────────────────────────────────────────────────────┘\n")

	// 匹配率
	matchRate := float64(result.Summary.MatchedFields) / float64(result.Summary.TotalFields) * 100
	builder.WriteString(fmt.Sprintf("\n📈 匹配率: %.1f%%\n", matchRate))

	if len(result.Differences) > 0 {
		builder.WriteString("\n🔍 详细差异分析:\n")
		builder.WriteString("═══════════════════════════════════════════════════════════════\n")

		// 按差异类型分组显示
		missingInGo := []FieldDifference{}
		valueDiffs := []FieldDifference{}
		otherDiffs := []FieldDifference{}

		for _, diff := range result.Differences {
			switch diff.DiffType {
			case "missing_in_go":
				missingInGo = append(missingInGo, diff)
			case "value_diff":
				valueDiffs = append(valueDiffs, diff)
			default:
				otherDiffs = append(otherDiffs, diff)
			}
		}

		// 显示Go版本缺失的字段
		if len(missingInGo) > 0 {
			builder.WriteString(fmt.Sprintf("\n❌ Go版本缺失字段 (%d个):\n", len(missingInGo)))
			for i, diff := range missingInGo {
				builder.WriteString(fmt.Sprintf("  %d. %s\n", i+1, diff.Path))
				builder.WriteString(fmt.Sprintf("     PHP值: %v\n", formatValue(diff.PHPValue)))
				builder.WriteString(fmt.Sprintf("     说明: %s\n", diff.Description))
				builder.WriteString("     ─────────────────────────────────────────────────\n")
			}
		}

		// 显示值不同的字段
		if len(valueDiffs) > 0 {
			builder.WriteString(fmt.Sprintf("\n🔄 值差异字段 (%d个):\n", len(valueDiffs)))
			for i, diff := range valueDiffs {
				builder.WriteString(fmt.Sprintf("  %d. %s\n", i+1, diff.Path))
				builder.WriteString(fmt.Sprintf("     PHP值: %v\n", formatValue(diff.PHPValue)))
				builder.WriteString(fmt.Sprintf("     Go值:  %v\n", formatValue(diff.GoValue)))
				builder.WriteString(fmt.Sprintf("     说明: %s\n", diff.Description))
				builder.WriteString("     ─────────────────────────────────────────────────\n")
			}
		}

		// 显示其他类型差异
		if len(otherDiffs) > 0 {
			builder.WriteString(fmt.Sprintf("\n⚠️  其他差异 (%d个):\n", len(otherDiffs)))
			for i, diff := range otherDiffs {
				builder.WriteString(fmt.Sprintf("  %d. %s [%s]\n", i+1, diff.Path, diff.DiffType))
				builder.WriteString(fmt.Sprintf("     PHP值: %v\n", formatValue(diff.PHPValue)))
				builder.WriteString(fmt.Sprintf("     Go值:  %v\n", formatValue(diff.GoValue)))
				builder.WriteString(fmt.Sprintf("     说明: %s\n", diff.Description))
				builder.WriteString("     ─────────────────────────────────────────────────\n")
			}
		}
	} else {
		builder.WriteString("\n✨ 恭喜！所有字段都完全匹配，数据一致性验证通过！\n")
	}

	builder.WriteString("\n═══════════════════════════════════════════════════════════════\n")
	return builder.String()
}

// FormatComparisonSummary 格式化对比结果摘要（简洁版本）
func FormatComparisonSummary(result *ComparisonResult) string {
	var builder strings.Builder

	if result.IsEqual {
		builder.WriteString("🎉 数据对比结果: ✅ 完全一致")
	} else {
		matchRate := float64(result.Summary.MatchedFields) / float64(result.Summary.TotalFields) * 100
		builder.WriteString(fmt.Sprintf("⚠️ 数据对比结果: ❌ 发现差异 (匹配率: %.1f%%)", matchRate))

		if result.Summary.MissingInGo > 0 {
			builder.WriteString(fmt.Sprintf("\n   • Go版本缺失字段: %d个", result.Summary.MissingInGo))
		}
		if result.Summary.ValueDifferences > 0 {
			builder.WriteString(fmt.Sprintf("\n   • 值差异字段: %d个", result.Summary.ValueDifferences))
		}
		if result.Summary.TableDataDiffs > 0 {
			builder.WriteString(fmt.Sprintf("\n   • TableData差异: %d个", result.Summary.TableDataDiffs))
		}
		if result.Summary.TableHeaderDiffs > 0 {
			builder.WriteString(fmt.Sprintf("\n   • TableHeader差异: %d个", result.Summary.TableHeaderDiffs))
		}
	}

	return builder.String()
}

// formatValue 格式化值的显示
func formatValue(value interface{}) string {
	if value == nil {
		return "<nil>"
	}

	switch v := value.(type) {
	case string:
		if len(v) > 50 {
			return fmt.Sprintf("\"%s...\" (长度:%d)", v[:47], len(v))
		}
		return fmt.Sprintf("\"%s\"", v)
	case []interface{}:
		return fmt.Sprintf("数组[长度:%d]", len(v))
	case map[string]interface{}:
		return fmt.Sprintf("对象[字段数:%d]", len(v))
	default:
		str := fmt.Sprintf("%v", v)
		if len(str) > 50 {
			return fmt.Sprintf("%s... (长度:%d)", str[:47], len(str))
		}
		return str
	}
}
